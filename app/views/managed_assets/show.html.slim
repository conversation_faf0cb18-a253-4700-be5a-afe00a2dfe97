- content_for :title
  | Assets

- content_for :additional_body_class
  | module--assets

.container
  #assets

= javascript_pack_tag "assets", async: true
= stylesheet_pack_tag "assets"

javascript:
  document.body.addEventListener("click", function(e) {
    var dissmissibles = document.querySelectorAll(".dissmissible-menu");
    for (let idx = 0; idx < dissmissibles.length; idx++) {
      const item = dissmissibles[idx];
      if (item) {
        item.classList.add('hidden');
      }
    }
  });

  document.body.addEventListener("keyup", function(e) {
    if (e.keyCode === 27) {
      var dissmissibles = document.querySelectorAll(".dissmissible-menu");
      for (let idx = 0; idx < dissmissibles.length; idx++) {
        const item = dissmissibles[idx];
        if (item) {
          item.classList.add('hidden');
        }
      }
    }
  });
