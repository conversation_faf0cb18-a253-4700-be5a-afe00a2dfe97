class ManagedAssetsController < Assets::BaseController
  include <PERSON><PERSON><PERSON><PERSON><PERSON>
  include PostgresHelper
  include AssetLifecycleHelper
  include HandleCompanyCacheKeys

  before_action :authorize_write, only: [:create, :update, :destroy, :new]
  skip_before_action :verify_authenticity_token, only: [:destroy]
  before_action :set_meraki_client, only: [:usage_history, :latency_history]
  before_action :set_resource,
                only: [:archive, :unarchive, :show, :edit, :update, :destroy, :sources_summary, :usage_history, :latency_history, :asset_usage_history],
                if: Proc.new { |w| request.format.json? }

  def index
    respond_to do |format|
      format.html {}
      format.json { render json: managed_assets }
    end
  end

  def previously_used_assets_by_user
    assets, total = fetch_previous_assets_of_user(params[:contributor_id], params[:page_no], params[:page_size], :used_by_contributor_id)
    render json: { assets: assets, total_assets: total }
  end
  
  def previously_managed_assets_by_user
    assets, total = fetch_previous_assets_of_user(params[:contributor_id], params[:page_no], params[:page_size], :managed_by_contributor_id)
    render json: { assets: assets, total_assets: total }
  end
  
  def new
    respond_to do |format|
      format.json do
        @managed_asset = scoped_company.managed_assets.new
        render json: @managed_asset.get_asset_info_json
      end
      format.html { render action: 'show' }
    end
  end

  def edit
    respond_to do |format|
      format.json { render json: @managed_asset.get_asset_info_json }
      format.html { render action: 'show' }
    end
  end

  def import
    render json: {}, status: :ok
  end

  def import_class(my_type)
    my_class = my_type.to_s.titleize.gsub(/\s+/, "")
    "ImportExport::Assets::#{my_class}".constantize
  end

  def send_app_to_all
    selected_user_ids = params[:ids]
    scoped_company.company_users.where(id: selected_user_ids).update_all(self_onboarding: 3)
    selected_user_ids.each_slice(10).with_index do |batch, index|
      SendAppToAllWorker.perform_in((index*30).seconds, batch, scoped_company_user.full_name)
    end
    render json: { status: true }
  end

  def windows_exe_available
    windows_self_on_boarding = if is_cache_enabled?('desktop_app_release')
                                 key = "Windows_self_on_boarding_app_release"
                                 Rails.cache.fetch(key, expires_in: 8.hours, skip_nil: true) do
                                  AppRelease.Windows.where(app_name: "self_on_boarding").last
                                 end
                               else
                                 AppRelease.Windows.where(app_name: "self_on_boarding").last
                               end
    mac_exe = if is_cache_enabled?('desktop_app_release')
                key = "MAC_self_on_boarding_app_release"
                Rails.cache.fetch(key, expires_in: 8.hours, skip_nil: true) do
                  AppRelease.MAC.where(app_name: "self_on_boarding").last
                end
              else
                AppRelease.MAC.where(app_name: "self_on_boarding").last
              end
    render json: {
      available: windows_self_on_boarding.present? || mac_exe.present?,
      windows_self_on_boarding: windows_self_on_boarding&.version,
      mac_self_on_boarding: mac_exe&.version
    }
  end

  def create
    check_default_vendor
    @asset = scoped_company.managed_assets.manually_added.new(managed_asset_params)
    @asset.custom_status = scoped_company.asset_statuses.find_by_name('In Use')

    @asset.creator_id = scoped_company_user&.contributor&.id
    if @asset.location_id.nil? && @asset.assignment_information&.user&.contributor_type == "CompanyUser"
      @asset.location_id = @asset.assignment_information&.user&.company_user&.location&.id 
    end

    @asset.created_by = scoped_company_user # used for reward points

    tags_array = get_asset_tags
    if tags_array.present?
      all_asset_tags = scoped_company.asset_tags.pluck(:name, :id).to_h
      new_company_tags = tags_array - all_asset_tags.keys
      tags_data = new_company_tags.map { |tag| { name: tag, company_id: scoped_company.id } }
      CompanyAssetTag.upsert_all(tags_data, unique_by: [:name, :company_id]) if tags_data.present?

      scoped_company.asset_tags.where(name: tags_array).each do |tag|
        @asset.managed_asset_tags << ManagedAssetTag.new(company_asset_tag_id: tag.id)
      end
    end

    add_alerts

    if params[:managed_asset][:parent_asset_id].present? && params[:managed_asset][:parent_asset_id] != "null"
      clone_remaining_asset_data(params[:managed_asset][:parent_asset_id], @asset)
    end

    if @asset.save_without_auditing
      create_new_vendor
      create_default_vendor_source
      save_related_items @asset if params[:managed_asset][:links_array].present?
      create_lifecycle_audit if should_lifecycle_apply?
      render json: { message: "Asset was successfully created", asset: @asset.get_asset_info_json }
    else
      scoped_company.vendors.find(@vendor_id).destroy if @vendor_create_flag
      render json: { message: @asset.errors.full_messages.join(".  ") }, status: :bad_request
    end
  end

  def clone_remaining_asset_data(parent_asset_id, new_asset)
    parent_asset = ManagedAsset.find_by(id: parent_asset_id)
    return if parent_asset.blank?
  
    clone_asset_data_if_same_type(parent_asset, new_asset)
    clone_attachments(parent_asset, new_asset) if params[:managed_asset][:managed_asset_attachments].present?
    clone_asset_image(parent_asset, new_asset) if parent_asset.image.attached? && !params[:image].present? && params[:managed_asset][:destroy_image] != 'true'
  end

  def show
    respond_to do |format|
      format.html {  }
      format.json { render json: @managed_asset.get_asset_info_json }
    end
  end

  def latency_history
    if scoped_company.meraki_config.present?
      device_serial = @managed_asset['machine_serial_number']
      response = @meraki_client.device_latency_history(device_serial, nil)
      if response[:status] == :ok
        render json: response[:filtered_response].as_json, status: :ok
      else
        render json: response.parsed_response['errors'], status: :bad_request
      end
    else
      render json: { message: 'Config not present' }, status: :not_found
    end
  end

  def usage_history
    if scoped_company.meraki_config.present?
      from = params['date']    # The start time of the query range
      resolution = params['resolution']    # The time resolution in seconds for returned data

      date_format = params['resolution'].to_i == 3600 ? '%H:%M' : '%d %b'  # DateTime format in which the display date should be
      network_id = @managed_asset.details['Network Id']
      device_serial = @managed_asset['machine_serial_number']
      if @managed_asset.asset_type.name == 'Firewall'
        response = @meraki_client.firewall_usage_history(network_id, from, resolution, date_format)
      elsif @managed_asset.asset_type.name == 'Switch'
        clients_mac_add = @meraki_client.device_clients(device_serial, from).pluck("id")
        response = @meraki_client.switch_client_usage_history(network_id, clients_mac_add, from, resolution, date_format)
      else  #will only run for meraki WAP device type
        response = @meraki_client.ap_device_usage_history(network_id, device_serial, from, resolution, date_format)
      end

      if response[:status] == :ok
        render json: response[:filtered_response].as_json, status: :ok
      else
        render json: response.parsed_response['errors'], status: :bad_request
      end
    else
      render json: { message: 'Config not present' }, status: :not_found
    end
  end

  def set_meraki_client
    @meraki_client ||= Integrations::Meraki::FetchData.new(scoped_company.meraki_config, false)
  end

  def get_associated_tickets_history
    help_ticket_ids = params["helpTicketIds"].map(&:to_i)
    sql = """
      SELECT
        ht.id as id,
        ht.ticket_number as ticket_number,
        subject_values.value_str as subject,
        status_values.value_str as status,
        ht.created_at as created_at
      FROM
        help_tickets as ht
      LEFT JOIN custom_form_fields as statuses
        ON statuses.custom_form_id = ht.custom_form_id
        AND statuses.name = 'status'
      LEFT JOIN custom_form_values as status_values
        ON status_values.custom_form_field_id = statuses.id
        AND status_values.module_type = 'HelpTicket'
        AND status_values.module_id = ht.id
      LEFT JOIN custom_form_fields as subjects
        ON subjects.custom_form_id = ht.custom_form_id
        AND subjects.name = 'subject'
      LEFT JOIN custom_form_values as subject_values
        ON subject_values.custom_form_field_id = subjects.id
        AND subject_values.module_type = 'HelpTicket'
        AND subject_values.module_id = ht.id
      WHERE ht.id IN (?)
      ORDER BY ht.created_at ASC
    """
    query = ActiveRecord::Base.send(:sanitize_sql_array, [sql, help_ticket_ids])
    associated_tickets_history = ActiveRecord::Base.connection.execute(query)
    render json: associated_tickets_history.as_json
  end

  def update
    @asset = @managed_asset
    check_default_vendor
    @managed_asset.transaction do
      if (managed_asset_params[:company_asset_type_id].present? && managed_asset_params[:company_asset_type_id].to_i != @managed_asset.company_asset_type_id)
        @managed_asset&.hardware_detail&.destroy if @managed_asset&.hardware_detail&.present?
        @managed_asset.reload
        asset_type = CompanyAssetType.find(managed_asset_params[:company_asset_type_id])
        @managed_asset.hardware_detail = asset_type.hardware_detail_class.new(managed_asset_id: @managed_asset.id)
      end

      alerts = params[:managed_asset][:alert_dates_attributes]
      if alerts.present?
        alerts.each do |key, alert|
          alert_date = @managed_asset.alert_dates.find_by(id: alert[:id])
          if alert_date.present?
            alert_date.update(date: alert[:date], notes: alert[:notes])
          else
            @managed_asset.alert_dates.create(date: alert[:date], notes: alert[:notes])
            create_managed_asset_audit(@managed_asset, alert[:date])
          end
        end
      end

      if params[:managed_asset][:tags_array].present?
        new_tags_ids, deleted_tags_ids = handle_asset_tags
        ActiveRecord::Base.transaction do
          @managed_asset.managed_asset_tags.where(id: deleted_tags_ids).destroy_all
          new_tags_ids.each do |tag_id|
            @managed_asset.managed_asset_tags << ManagedAssetTag.new(company_asset_tag_id: tag_id)
          end
          ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
        rescue Exception => e
          Rails.logger.error("Transaction failed: #{e.message}")
          ActiveRecord::Base.connection.execute "ROLLBACK"
        end
      end

      if should_lifecycle_apply?
        @managed_asset.cost&.depreciation&.destroy
      end

      unless params[:managed_asset][:managed_asset_attachments].nil?
        exclude_ids = params[:managed_asset][:managed_asset_attachments]&.split(',')
        if exclude_ids.blank?
          @managed_asset.managed_asset_attachments.destroy_all
        else
        @managed_asset.managed_asset_attachments.where.not(id: exclude_ids)&.destroy_all
        end
      end
      if managed_asset_params[:image].blank? && params[:managed_asset][:destroy_image].present?
        @managed_asset.image.purge
      end

      if managed_asset_params[:managed_asset_attachments_attributes].present?
        @managed_asset.update(managed_asset_attachments_attributes: managed_asset_params[:managed_asset_attachments_attributes])
      end

      save_related_items @managed_asset if params[:managed_asset].key?(:links_array)

      @asset_updated = @managed_asset.update(managed_asset_params.except(:managed_asset_attachments_attributes, :alert_dates_attributes))
      create_usage_history if params[:create_usage_history]
      create_new_vendor
      create_default_vendor_source
    rescue Exception => e
      scoped_company.vendors.find(@vendor_id).destroy if @vendor_create_flag
      ManagedAsset.connection.execute "ROLLBACK"
      raise e
    end

    if @asset_updated
      render json: { asset: @managed_asset.get_asset_info_json }
    else
      render json: { message: @managed_asset.errors.full_messages.join(",  ") }, status: :unprocessable_entity
    end
  end

  def destroy
    if @managed_asset.destroy
      scoped_company.discovered_assets.where(managed_asset_id: @managed_asset).destroy_all
      render json: {}, status: :ok
    else
      render json: { message: @managed_asset.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end

  def archive
    if @managed_asset.update_column('archived', true)
      create_audit_object(true)
      render json: {}, status: :ok
    else
      render json: { message: @managed_asset.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end

  def unarchive
    if @managed_asset.update_column('archived', false)
      create_audit_object(false)
      render json: {}, status: :ok
    else
      render json: { message: @managed_asset.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end

  def remove_alert
    alert = AlertDate.find_by(id: params[:alert_id])
    if alert.present?
      if alert.destroy
        render json: {}, status: :ok
      else
        render json: { message: alert.errors.full_messages.to_sentence }, status: :unprocessable_entity
      end
    else
      render json: { message: "Alert not found" }, status: :not_found
    end
  end

  def bulk_asset_types_update
    errors = []
    managed_assets = params[:assets]
    if managed_assets
      managed_assets.each do |managed_asset|
        asset = ManagedAsset.unscoped.find_by(id: managed_asset[:id])
        asset.hardware_detail.destroy if asset.hardware_detail.present?
        asset.reload
        asset_type = CompanyAssetType.find_by(id: managed_asset[:company_asset_type_id].to_i)
        asset.hardware_detail = asset_type.hardware_detail_class.new(managed_asset_id: asset.id)
        asset.asset_type = asset_type
        unless asset.save
          errors << asset.errors.full_messages.to_sentence
        end
      end
      if errors.length > 0
        render json: { message: errors }, status: :unprocessable_entity
      else
        render json: {}, status: :ok
      end
    end
  end

  def bulk_status_update
    errors = []
    managed_assets = params[:assets]
    if managed_assets.present?
      managed_assets.each do |managed_asset|
        asset = ManagedAsset.unscoped.find_by(id: managed_asset[:id])
        status = CompanyAssetStatus.find_by(id: managed_asset[:company_asset_status_id].to_i)
        asset.custom_status = status
        unless asset.save
          errors << asset.errors.full_messages.to_sentence
        end
      end
      if errors.length > 0
        render json: { message: errors }, status: :unprocessable_entity
      else
        render json: {}, status: :ok
      end
    end
  end

  def sources_summary
    attributes_desc = @managed_asset.attributes_description
    return render json: [], status: :not_found if attributes_desc.blank?
  
    updated_data = attributes_desc.data.deep_dup  
    latest_data = @managed_asset.asset_sources.last
  
    if latest_data.present?
      keys = %w[machine_group_id last_logged_in_user last_check_in_time product_name]
      keys.each do |key|
        updated_data << {
          "key" => key.camelize(:lower),
          "value" => latest_data.asset_data[key],
          "source" => latest_data.source
        }
      end
    end
    render json: attributes_desc.as_json.merge(data: updated_data), status: :ok
  end

  def asset_usage_history
    result = ManagedAssets::AssetUsageHistory.new(@managed_asset, params).call
    return render json: result, status: :ok
  end

  def download_mac_agent
    @version = mac_agent_version
    if @version.present?
      respond_to do |format|
        format.json { render json: { status: true,
                                    url: @version.installer_link.url,
                                    login_code: current_user.login_code,
                                    sso_user: current_user.sso_user,
                                    app_version: @version.version } }
      end
    else
      respond_to do |format|
        format.json { render json: { status: false } }
      end
    end
  end

  def get_latest_windows_agent_version
    windows_agent_version  = if is_cache_enabled?('desktop_app_release')
                               key = "Windows_agent_app_release"
                               Rails.cache.fetch(key, expires_in: 8.hours, skip_nil: true) do
                                 AppRelease.Windows.where(app_name: "agent").last
                               end
                             else
                               AppRelease.Windows.where(app_name: "agent").last
                             end
    render json: windows_agent_version&.version , status: :ok
  end

  def get_asset_report_record
    custom_report = fetch_latest_custom_report(params[:contributor_id])

    if custom_report.present?
      render json: {
        report: {
          id: custom_report.id,
          name: custom_report.name,
          percentage: custom_report.percentage,
          ready_to_download: custom_report.ready_to_download,
          content_type: custom_report.report_content_type,
          download_link: custom_report.download_link,
          trackId: custom_report.track_id
        }
      }, status: :ok
    else
      render json: { message: 'Custom Report not found' }, status: :not_found
    end
  end

  def reset_download_link
    custom_report = fetch_latest_custom_report(params[:contributor_id])
  
    if custom_report.present?
      custom_report.update(download_link: '', ready_to_download: false)
      render json: { message: 'Download link reset successfully' }, status: :ok
    else
      render json: { message: 'Custom Report not found' }, status: :not_found
    end
  end

  def should_display_people_tab
    service_option = ServiceOption.find_by(service_name: "managed_assets/people_tab")
    render json: { display_people_tab: !service_option.status, status: 200 }
  end

  private
  
  def clone_asset_data_if_same_type(parent_asset, new_asset)
    return unless parent_asset.company_asset_type_id == new_asset.company_asset_type_id
  
    clone_asset_softwares(parent_asset, new_asset) if param_enabled?(:asset_softwares) && parent_asset.asset_softwares.any?
    clone_custom_detail(parent_asset, new_asset) if param_enabled?(:custom_detail) && parent_asset.details.present?
    clone_system_detail(parent_asset, new_asset) if param_enabled?(:system_detail) && parent_asset.system_details.any?
    clone_cloud_detail(parent_asset, new_asset) if param_enabled?(:cloud_detail) && parent_asset.cloud_asset_attributes.any?

    if (param_enabled?(:system_detail) || param_enabled?(:cloud_detail)) && parent_asset.asset_sources.any?
      clone_asset_sources(parent_asset, new_asset)
    end
  end
  
  def clone_asset_softwares(parent_asset, new_asset)
    softwares = parent_asset.asset_softwares.map do |s|
      {
        name: s.name,
        software_type: s.software_type,
        product_key: s.product_key,
        install_date: s.install_date,
        is_manual: s.is_manual,
        discovered_asset_id: s.discovered_asset_id
      }
    end
  
    new_asset.asset_softwares.build(softwares)
  end
  
  def clone_custom_detail(parent_asset, new_asset)
    new_asset.details = parent_asset.details
  end
  
  def clone_system_detail(parent_asset, new_asset)
    system_details = parent_asset.system_details.map do |detail|
      {
        discovered_asset_id: detail.discovered_asset_id,
        detail_category: detail.detail_category,
        detail_data: detail.detail_data
      }
    end
  
    new_asset.system_details.build(system_details)
  end
  
  def clone_asset_sources(parent_asset, new_asset)
    sources = parent_asset.asset_sources.map do |source|
      {
        source: AssetSource.sources[source.source.to_sym],
        asset_data: source.asset_data
      }
    end
  
    new_asset.asset_sources.build(sources)
  end
  
  def clone_cloud_detail(parent_asset, new_asset)
    cloud_attributes = parent_asset.cloud_asset_attributes.map do |attr|
      {
        key: attr.key,
        value: attr.value,
        discovered_asset_id: attr.discovered_asset_id
      }
    end
  
    new_asset.cloud_asset_attributes.build(cloud_attributes)
  end
  
  def clone_attachments(parent_asset, new_asset)
    attachment_ids = params[:managed_asset][:managed_asset_attachments].to_s.split(',').map(&:to_i)
  
    parent_asset.managed_asset_attachments.where(id: attachment_ids).each do |attachment|
      new_attachment = new_asset.managed_asset_attachments.build
  
      if attachment.library_document.present?
        new_attachment.library_document = attachment.library_document
      elsif attachment.attached_file.attached?
        new_attachment.attached_file.attach(
          io: StringIO.new(attachment.attached_file.download),
          filename: attachment.attached_file.filename.to_s,
          content_type: attachment.attached_file.content_type
        )
      end
    end
  end
  
  def clone_asset_image(parent_asset, new_asset)
    new_asset.image.attach(
      io: StringIO.new(parent_asset.image.download),
      filename: parent_asset.image.filename.to_s,
      content_type: parent_asset.image.content_type
    )
  end
  
  def param_enabled?(key)
    params[:managed_asset][key].present? && params[:managed_asset][key] == "true"
  end  

  def fetch_previous_assets_of_user(contributor_id, page_no, page_size, contributor_type)
    used_asset_ids = AssetUsageHistory.where(contributor_type => contributor_id).select(:managed_asset_id).distinct
    assigned_asset_ids = AssignmentInformation.where(contributor_type => contributor_id).select(:managed_asset_id)
    assets = ManagedAsset.where(id: used_asset_ids).where.not(id: assigned_asset_ids)
    paginated_assets = assets.offset((page_no.to_i - 1) * page_size.to_i).limit(page_size.to_i)
    [paginated_assets, assets.count]
  end

  def fetch_latest_custom_report(user_id)
    CustomReport.where(creator_id: user_id)
                .where.not(percentage: nil)
                .order(created_at: :desc)
                .first
  end

  def create_managed_asset_audit(managed_asset, alert_date)
    audit = Audited::Audit.new
    audit[:auditable_id] = managed_asset.id
    audit[:auditable_type] = "ManagedAsset"
    audit[:user_id] = scoped_company_user.id
    audit[:user_type] = 'CompanyUser'
    audit[:action] = 'create'
    audit[:audited_changes] = {'date' => [nil, alert_date]}
    audit[:remote_address] = request.remote_ip
    audit[:request_uuid] = request.uuid
    audit.save!
  end

  def mac_agent_version
    is_admin_app = JSON.parse(params[:is_admin_agent])
    if is_cache_enabled?('desktop_app_release')
      key = is_admin_app ? "MAC_agent_admin_app_release" : "MAC_agent_non_admin_app_release"
      Rails.cache.fetch(key, expires_in: 8.hours, skip_nil: true) do
        AppRelease.MAC.where(is_admin_app: is_admin_app, is_package_build: true).last
      end
    else
      AppRelease.MAC.where(is_admin_app: is_admin_app, is_package_build: true).last
    end
  end

  def create_lifecycle_audit
    lifecycle_obj = lifecycle_audits(
      @asset.cost.id,
      @asset.id,
      scoped_company_user.id,
      [nil, @asset.cost.asset_lifecycle_id],
      request.remote_ip,
      request.uuid
    )
    Audited::Audit.create!(lifecycle_obj)
  end

  def check_default_vendor
    default_vendor_id = params['managed_asset']['default_vendor_id']
    @vendor_create_flag = false
    if default_vendor_id.present?
      default_vendor_id = params['managed_asset']['default_vendor_id'].to_i
      default_vendor = DefaultVendor.find_by(id: default_vendor_id)
      vendor = scoped_company.vendors.find_by_name(default_vendor.name)
      if vendor
        @vendor_id = vendor.id
      else
        @vendor_id = scoped_company.vendors.create!(
          name: default_vendor.name,
          created_by: scoped_company_user,
          creator_id: scoped_company_user&.contributor&.id,
          category_id: add_category(default_vendor)
        ).id
        @vendor_create_flag = true
      end
      params['managed_asset']['vendor_id'] = @vendor_id
    end
  end

  def add_category(default_vendor)
    category_name = default_vendor&.default_category&.name || "Uncategorized"
    scoped_company.categories.find_by(name: category_name)&.id
  end

  def create_usage_history
    @asset.asset_usage_histories.create!(usage_history_params)
  end

  def create_new_vendor
    vendor_name = params['managed_asset']['new_vendor_name']
    existing_vendor = scoped_company.vendors.find_by_name(vendor_name)
    if vendor_name.present? && !existing_vendor
      begin
        category_id = scoped_company.categories.find_by(name: "Uncategorized").id
        new_vendor = Vendor.create!(
          name: vendor_name,
          category_id: category_id,
          company_id: scoped_company.id,
          creator_id: scoped_company_user&.contributor&.id,
          vendor_source: @asset
        )
        @asset.update_column(:vendor_id, new_vendor.id)
      rescue Exception => e
        Rails.logger.error("Failed to create vendor: #{e.message}")
        raise e
      end
    elsif vendor_name.present?
      @asset.update_column(:vendor_id, existing_vendor.id)
    end
  end

  def create_default_vendor_source
    if params['managed_asset']['default_vendor_id'].present?
      vendor = Vendor.find(@vendor_id)
      vendor.vendor_source = @asset
      vendor.save!
    end
  end

  def save_related_items(managed_asset)
    LinkableService.new(managed_asset, scoped_company_user, request).save_serialized_related_items(params[:managed_asset][:links_array])
  end

  def set_resource
    if params[:id] != "connectors" && request.format.json?
      @managed_asset = ManagedAsset.unscoped.where(id: params[:id], company_id: scoped_company.id).first
      unless @managed_asset
        respond_to do |format|
          format.json { render json: { message: "Asset was not found." }, status: :not_found }
          format.html { render 'shared/not_found' }
        end
      end
    end
  end

  def usage_history_params
    managed_asset_params[:assignment_information_attributes].except(:id).merge(company_asset_status_id: managed_asset_params[:company_asset_status_id])
  end

  def should_lifecycle_apply?
    asset_lifecycle_id = params.dig(:managed_asset, :cost_attributes, :asset_lifecycle_id)
    asset_lifecycle_id != 'null' && asset_lifecycle_id.present?
  end

  def managed_asset_params
    params.require(:managed_asset).permit(
      :id,
      :contributor_id,
      :name,
      :operating_system,
      :location_id,
      :company_asset_status_id,
      :company_asset_type_id,
      :tags,
      :tags_array,
      :description,
      :impact,
      :asset_tag,
      :serial_number,
      :manufacturer,
      :manufactured_by,
      :model,
      :active_tab,
      :company_id,
      :machine_serial_number,
      :product_number,
      :acquisition_date,
      :warranty_expiration,
      :vendor_id,
      :hardware_detail_id,
      :hardware_detail_type,
      :install_date,
      :status,
      :image,
      :ip_address,
      :mac_addresses_text,
      :sku,
      :firmware,
      :source,
      :system_up_time,
      :system_uuid,
      :company_asset_type_ids,
      :active_tags,
      :location_ids,
      :statuses,
      :warranty_statuses,
      :archives,
      :sources,
      :department_ids,
      managed_asset_attachments_attributes: [
        :attached_file
      ],
      cost_attributes: [
        :id,
        :salvage,
        :depreciation_id,
        :purchase_price,
        :replacement_cost,
        :useful_life,
        :approaching_end_of_life,
        :po,
        :lifecycle_type,
        :asset_lifecycle_id
      ],
      assignment_information_attributes: [
        :id,
        :location_id,
        :department_id,
        :managed_by_contributor_id,
        :used_by_contributor_id,
        :phone_number,
        :phone_number_country_code,
        :phone_number_country_code_number,
        :current_state_since,
        :expected_check_in
      ],
      hardware_detail_attributes: [
        :id,
        :processor,
        :hard_drive,
        :memory,
        :serial_number,
        :connection_interface,
        :screen_size,
        :network,
        :ports,
        :imei,
        :device_id,
        :disk_encryption,
        :disk_free_space,
        :processor_logical_cores,
        :processor_cores,
        :processor_architecture,
        :hostname,
        :domain_name,
        :remote_ip_address,
        :hardware_version
      ],
      alert_dates_attributes: [:id, :date, :notes]
    )
  end

  def managed_assets
    ManagedAssets::AssetsQuery::AssetsQuery.new(scoped_company, scoped_companies, scoped_company_user, permissions, params).call
  end

  def create_audit_object(archived)
    a = Audited::Audit.new
    a[:auditable_id] = params[:id]
    a[:auditable_type] = "ManagedAsset"
    a[:user_id] = scoped_company_user.id
    a[:user_type] = 'CompanyUser'
    a[:action] = 'update'
    a[:audited_changes] = {"archived" => [!archived, archived]}
    a[:remote_address] = request.remote_ip
    a[:request_uuid] = request.uuid
    a.save!
  end

  def add_alerts
    alerts = params[:managed_asset][:alert_dates]
    if alerts.present?
      alerts.each do |key, alert|
        @asset.alert_dates << AlertDate.new(date: alert[:date], notes: alert[:notes])
      end
    end
  end

  def get_asset_tags
    tag_object = params.dig(:managed_asset, :tags_array)
    return [] unless tag_object

    if tag_object.is_a?(Array)
      tag_object.map(&:downcase)
    else
      tag_object&.values&.map(&:downcase)
    end
  end

  def handle_asset_tags
    requested_tags = get_asset_tags
    company = @managed_asset.company
    new_company_asset_tags = requested_tags - company.asset_tags.pluck(:name)&.map(&:downcase)
    new_tags_data = new_company_asset_tags.map { |tag| { name: tag, company_id: company.id } }
    CompanyAssetTag.upsert_all(new_tags_data, unique_by: [:name, :company_id]) if new_tags_data.present?

    asset_tags = @managed_asset.managed_asset_tags.joins(:company_asset_tag).select('managed_asset_tags.id as mat_id, company_asset_tags.name as name')
    existing_tags = asset_tags.map(&:name)&.map(&:downcase)
    new_tags = requested_tags - existing_tags
    deleted_tags = existing_tags - requested_tags
    new_tags_ids = scoped_company.asset_tags.where(name: new_tags).pluck(:id)
    deleted_tags_ids = asset_tags.select { |tag| deleted_tags.include?(tag.name) }.map(&:mat_id)
    [new_tags_ids, deleted_tags_ids]
  end
end
