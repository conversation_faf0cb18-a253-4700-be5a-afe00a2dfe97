module AutomatedTasks
  class TaskActionsWorker
    include Sidekiq::Job
    include UnusedMemoryUtils
    include AutomatedTaskErrorLog
    extend ::NewRelic::Agent::MethodTracer

    sidekiq_options queue: 'at_actions'

    def perform(action_id, object_id, object_class_name, type_name)
      @action_id = action_id
      @object_id = object_id
      @object_class_name = object_class_name
      @type_name = type_name

      NewRelic::Agent.add_custom_attributes(
        'action_id' => action_id,
        'object_id' => object_id,
        'type_name' => type_name,
        'object_class_name' => object_class_name
      )
      self.class.trace_execution_scoped(["Custom/task_actions_worker/#{type_name}/perform"]) do
        @action = AutomatedTasks::TaskAction.find_by(id: action_id)
        @object = object_class_name.constantize.find_by(id: object_id)
        return if GlobalEmailBlocking.last&.company_ids&.include?(@object.company.id)
        action&.call if @object.present? && @action.present?

        clear_unused_memory
      end
    rescue => e
      retry_params = {
        action_id: @action_id,
        object_id: @object_id,
        object_class_name: @object_class_name,
        type_name: @type_name,
        worker_class: self.class.name,
        queue: 'at_actions'
      }
      handle_task_error(e, @action.automated_task, 'TaskAction', @type_name, @object.company.id, retry_params)
    end

    def action
      "AutomatedTasks::Actions::#{@type_name}".constantize.new(@action, @object)
    end
  end
end
