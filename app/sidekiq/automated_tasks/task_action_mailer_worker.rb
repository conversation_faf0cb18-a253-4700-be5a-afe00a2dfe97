module AutomatedTasks
  class TaskActionMailerWorker
    include AutomatedTaskErrorLog
    include Sidekiq::Job

    sidekiq_options queue: 'at_actions'

    def perform(action_id, object_id, object_class_name, recipients, email_data)
      @action_id = action_id
      @object_id = object_id
      @object_class_name = object_class_name
      @recipients = recipients
      @email_data = email_data

      @action = AutomatedTasks::TaskAction.find_by(id: action_id)
      @object = object_class_name.constantize.find_by(id: object_id)
      return unless @object.present? && @action.present?
      return if GlobalEmailBlocking.last&.company_ids&.include?(@object.company.id)

      email_data['custom_form_attachments'] = email_data['custom_form_attachments'].map do |attachment|
        url = URI(attachment['file_url'])
        file = Net::HTTP.get(url)
        attachment.merge('file' => file)
      end

      # a.casfa
      retries = 0
      begin
        TaskActionMailer.action_email(@action, @object, recipients, email_data).deliver_now!
      rescue Net::SMTPFatalError, Net::SMTPSyntaxError => e
        log_email_error(recipients, e.message, email_data)
      rescue EOFError => e
        if retries <= 3
          sleep(5)
          retries = retries + 1
          retry
        end
      end
    rescue Net::SMTPFatalError, Net::SMTPSyntaxError => e
      log_email_error(recipients, e.message, email_data)
    rescue => e
      retry_params = {
        action_id: @action_id,
        object_id: @object_id,
        object_class_name: @object_class_name,
        recipients: @recipients,
        email_data: @email_data&.except('custom_form_attachments'), 
        type_name: 'SendEmail',
        worker_class: self.class.name,
        queue: 'at_actions'
      }
      handle_task_error(e, @action.automated_task, 'TaskAction', 'SendEmail', @object.company.id, retry_params)
    end

    private

    def log_email_error(recipients, error_message, email_data)
      if Rails.env.production? || Rails.env.staging?
        recipients.each do |recipient|
          Logs::EmailLog.create(
            company_id: @object.help_ticket.company.id,
            subject: email_data['subject'],
            body: email_data['body'],
            sender_email: email_data['from_email'],
            receiver_emails: [recipient],
            email_type: 'automated_task_email',
            status: 'error',
            error_message: error_message
          )
        end
      end
    end
  end
end
