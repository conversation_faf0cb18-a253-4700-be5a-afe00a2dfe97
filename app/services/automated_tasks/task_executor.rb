class AutomatedTasks::TaskExecutor
  include AutomatedTaskErrorLog
  extend ::NewRelic::Agent::MethodTracer

  attr_accessor :before, :object, :scheduled_tasks, :automated_task_id
  NOTIFICATION_ACTIONS = ['SendEmail', 'SendMsTeamsMessage', 'SendNotification', 'SendSlackMessage', 'SendSms']

  ##
  # The before param is a hash of attributes, while the object is the
  # actual object in question.
  def initialize(before, object, scheduled_tasks = false, automated_task_id = nil)
    self.before = before
    self.object = object
    self.scheduled_tasks = scheduled_tasks
    self.automated_task_id = automated_task_id
  end

  def call
    return if object.blank?
    potential_matches.each do |task|
      execute_task(task)
    rescue AutomatedTasks::TaskAction::HaltException
      break
    end
  end

  def execute_task(task)
    result = false
    self.class.trace_execution_scoped(["Custom/task_executor/execute_task_1/#{task.id}/#{object.id}"]) do
      task.task_events.each do |event|
        next if class_name == 'AutomatedTasks::ExecutionDate' && event.event_type.event_class != 'DateOccurred'
        if event.event_type.model == class_name.demodulize
          event_criteria = event(event)
          result = event_criteria&.match?(object, before)
        end
        break if result
      end
    end
    self.class.trace_execution_scoped(["Custom/task_executor/execute_task_2/#{task.id}/#{object.id}"]) do
      if result
        task.task_actions.order(id: :asc).each do |action|
          type_name = action.action_type.action_class
          next if NOTIFICATION_ACTIONS.include?(type_name) && object.present? && mute_notifications?
          if type_name == "HaltExecution" || class_name == 'AutomatedTasks::ExecutionDate'
            "AutomatedTasks::Actions::#{type_name}".constantize.new(action, object)&.call
          else
            AutomatedTasks::TaskActionsWorker.perform_async(action.id, object.id, class_name, type_name)
          end
        end
      end
    end
  rescue AutomatedTasks::TaskAction::HaltException
    return raise AutomatedTasks::TaskAction::HaltException
  rescue => e
    retry_params = {
      task_id: task.id,
      object_id: object.id,
      object_class_name: class_name,
      scheduled_tasks: scheduled_tasks,
      automated_task_id: automated_task_id,
      before_attributes: before,
      executor_class: self.class.name
    }
    handle_task_error(e, task, 'AutomatedTask', class_name, object.company.id, retry_params)
    # there are some tests to cover this so only re-raise for dev
    raise e if Rails.env.development?
  end

  def potential_matches
    NewRelic::Agent.add_custom_attributes(
      'potential_object_id' => object.id
    )
    self.class.trace_execution_scoped(['Custom/task_executor/potential_matches']) do
      tasks = workspace.automated_tasks
      tasks = tasks.where(disabled_at: nil) unless scheduled_tasks
      tasks = tasks.joins(task_events: :event_type)
      if scheduled_tasks
        @potential_matches ||= tasks.where(id: automated_task_id).uniq
      else
        @potential_matches ||= tasks
                                .where("automated_tasks_event_types.model = ?", class_name.demodulize)
                                .order(order: :asc)&.uniq
      end
    end
  end

  def event(event)
    type_name = event.event_type.event_class
    "AutomatedTasks::Events::#{type_name}".constantize.new(event)
  end

  def workspace
    object.workspace
  end

  def mute_notifications?
    return false if class_name == "AutomatedTasks::ExecutionDate"
    if ["HelpTicket", "HelpTicketComment"].include?(class_name)
      return object.mute_notification
    else
      return object.help_ticket.mute_notification
    end
  end

  def class_name
    @class_name ||= object.class.name
  end
end
