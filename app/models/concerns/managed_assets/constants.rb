module ManagedAssets
  module Constants
    extend ActiveSupport::Concern
    included do |base|
      base.const_set(:OS_LIST, [
        "Windows 98", "Windows Vista", "Windows XP", "Windows 7", "Windows 8", "Windows 8.1", "Windows 10", "Ubuntu 14.04",
        "Ubuntu 14.10", "Ubuntu 15.04", "Ubuntu 15.10", "Ubuntu 16.04", "Ubuntu 16.10", "Ubuntu 17.04", "RedHat", "Open Suse",
        "Cent OS", "Mac OSX", "Cent OS"
      ])

      base.const_set(:SELECTED_CARD_DATA, [
        { name: "asset_type", friendly_name: "Type", id: 1 },
        { name: "name", friendly_name: "Asset Name",  id: 2 },
        { name: "source", friendly_name: "Source", id: 3 },
        { name: "tags", friendly_name: "Tags", id: 4 },
        { name: "warranty_expiration", friendly_name: "Warranty Status", id: 5 },
        { name: "archived_tag", friendly_name: "Archived Tag", id: 6 },
        { name: "updated_at", friendly_name: "Last Update", id: 7 }
      ])

      base.const_set(:SELECTED_COLUMNS, [
        { name: "asset_type", friendly_name: "Type", id: 1, active: false },
        { name: "name", friendly_name: "Asset Name", type: "string", id: 2, active: false },
        { name: "source", friendly_name: "Source", type: "integer", id: 3, active: false },
        { name: "tags", friendly_name: "Tags", type: "string", id: 4, active: false },
        { name: "warranty_expiration", friendly_name: "Warranty Status", type: "string", id: 5, active: false }
      ])

      base.const_set(:UNSELECTED_COLUMNS, [
        { name: "asset_type", friendly_name: "Type", id: 1, active: false },
        { name: "name", friendly_name: "Asset Name", type: "string", id: 2, active: false },
        { name: "source", friendly_name: "Source", type: "integer", id: 3, active: false },
        { name: "tags", friendly_name: "Tags", type: "string", id: 4, active: false },
        { name: "warranty_expiration", friendly_name: "Warranty Status", type: "string", id: 5, active: false },
        { name: "description", friendly_name: "Notes", type: "string", id: 8, active: false },
        { name: "location_id", friendly_name: "Location", type: "integer", id: 9, active: false },
        { name: "department_id", friendly_name: "Department", type: "integer", id: 10, active: false },
        { name: "model", friendly_name: "Model", type: "string", id: 11, active: false },
        { name: "mac_addresses", friendly_name: "Mac Addresses", type: "text", id: 12, active: false },
        { name: "status", friendly_name: "Usage Status", type: "integer", id: 13, active: false },
        { name: "manufacturer", friendly_name: "Manufacturer", type: "string", id: 14, active: false },
        { name: "ip_address", friendly_name: "IP Address", type: "string", id: 15, active: false },
        { name: "operating_system", friendly_name: "Operating System", type: "string", id: 16, active: false },
        { name: "product_number", friendly_name: "Product Number", type: "string", id: 17, active: false },
        { name: "serial_number", friendly_name: "Serial Number", type: "string", id: 18, active: false },
        { name: "machine_serial_number", friendly_name: "Machine Serial Number", type: "string", id: 19, active: false },
        { name: "hardware_detail_type", friendly_name: "Hardware Detail type", type: "string", id: 21, active: false },
        { name: "impact", friendly_name: "Impact", type: "integer", id: 22, active: false },
        { name: "asset_tag", friendly_name: "Asset Tag", type: "string", id: 23, active: false },
        { name: "acquisition_date", friendly_name: "Acquisition Date", type: "date", id: 24, active: false },
        { name: "archived", friendly_name: "Archived", id: 25, type: "boolean", active: false },
        { name: "install_date", friendly_name: "Install Date", id: 27, type: "date", active: false },
        { name: "used_by_contributor_id", friendly_name: "Used By", id: 29, type: "string", active: false },
        { name: "managed_by_contributor_id", friendly_name: "Managed By", id: 30, type: "string", active: false },
        { name: "purchase_price", friendly_name: "Purchase Price", id: 31, type: "string", active: false },
        { name: "firmware", friendly_name: "Firmware", id: 32, type: "string", active: false },
        { name: "warranty_date", friendly_name: "Warranty Date", type: "string", id: 33, active: false },
        { name: "updated_at", friendly_name: "Last Update", type: "string", id: 34, active: false }
      ])

      base.const_set(:SELECTED_WIDGETS, [
        { name: "operating_system", friendly_name: "OS", id: 1 },
        { name: "applications", friendly_name: "Applications", id: 2 },
        { name: "availability", friendly_name: "Availability", id: 3 },
        { name: "location", friendly_name: "Location", id: 4 },
        { name: "warranty", friendly_name: "Warranty", id: 5 }
      ])

      base.const_set(:ALL_WIDGETS, [
        { name: "operating_system", friendly_name: "OS", id: 1 },
        { name: "applications", friendly_name: "Applications", id: 2 },
        { name: "availability", friendly_name: "Availability", id: 3 },
        { name: "location", friendly_name: "Location", id: 4 },
        { name: "warranty", friendly_name: "Warranty", id: 5 },
        { name: "model", friendly_name: "Model", id: 6 },
        { name: "asset_type", friendly_name: "Asset Type", id: 7 },
        { name: "department_id", friendly_name: "Department", id: 8 },
        { name: "cost", friendly_name: "Cost", id: 9 },
        { name: "firmware", friendly_name: "Firmware", id: 10 }
      ])

      base.const_set(:AUDITED_EXCEPT_LIST, [
        :created_at, :updated_at, :private_asset_attributes, :hardware_detail_id,
        :hardware_detail_type, :guid, :source, :update_by_source_at,
        :linkable_id, :primary_asset, :merged, :system_up_time
      ])

      base.const_set(:EXPIRING_SOON_MARK, 3.months)

      base.const_set(:LABELS_FOR_WIDGETS, {
        'Antivirus' => ['Protected Devices', 'Non-Protected Devices'],
        'RMM Software' => ['Last updated', 'Missing updates'],
        'Data Encryption' => ['Devices with encryption', 'Devices without encryption'],
        'Firewall' => ['Devices with firewall enabled', 'Devices with firewall not Enabled']
      })

      base.const_set(:RISK_SUMMARY_LABELS, {
        'Antivirus' => 'Devices missing antivirus protection',
        'RMM Software' => 'Devices missing patch updates',
        'Data Encryption' => 'Devices without encryption',
        'Firewall' => 'Devices with firewall not enabled'
      })

      base.const_set(:DEFAULT_RISK_WIDGET_OPTIONS, {
        'Antivirus' => [
          'Avast', 'AVG AntiVirus', 'Bitdefender Antivirus Plus', 'Bitdefender - GravityZone',
          'Check Point Harmony', 'Check Point Antivirus', 'Cisco - Cisco Secure Endpoint',
          'CrowdStrike - Falcon Platform', 'Eset Protect', 'Huntress Antivirus', 'Kaspersky',
          'Malwarebytes', 'ThreatDown', 'McAfee - MVISION', 'McAfee AntiVirus',
          'Microsoft Defender', 'Norton Antivirus', 'Cortex XDR', 'SentinelOne - Singularity Platform',
          'Sophos', 'Symantec', 'Trend Micro', 'Vipre', 'VMware Carbon Black', 'Webroot'
        ],
        'RMM Software' => [
          'Action1 RMM', 'Addigy', 'Atera', 'Barracuda RMM', 'ConnectWise RMM',
          'Datto RMM', 'Jamf', 'Jumpcloud', 'Kandji', 'Kaseya VSA', 'ManageEngine',
          'N-Able RMM', 'NinjaRMM', 'Pulseway', 'SolarWinds RMM', 'SuperOps', 'Syncro'
        ]
      })

      base.const_set(:DEFAULT_RISK_WIDGETS, [
        { name: 'Antivirus', description: "Preset selection of common antivirus software", icon: "genuicon-antivirus" },
        { name: 'RMM Software', description: "Remote management and monitoring", icon: "genuicon-patch-management" },
        { name: 'Data Encryption', description: "Keep your digital information secure", icon: "genuicon-data-encryption" },
        { name: 'Firewall', description: "Block threats before they get too close", icon: "genuicon-firewall" }
      ])

      base.const_set(:DISC_ASSET_SELECTED_COLUMNS, [
        { name: "display_name", friendly_name: "Asset Name", type: "string", id: 1, active: false },
        { name: "asset_type", friendly_name: "Type", id: 2, active: false },
        { name: "model", friendly_name: "Model", id: 3, active: false },
        { name: "manufacturer", friendly_name: "Manufacturer", id: 4, active: false },
        { name: "mac_addresses", friendly_name: "MAC Addresses", id: 5, active: false },
        { name: "integrations_locations_id", friendly_name: "Location", id: 6, active: false },
        { name: "machine_serial_no", friendly_name: "Serial No.", id: 7, active: false },
        { name: "source", friendly_name: "Sources", id: 8, active: false },
        { name: "created_at", friendly_name: "Discovered On", id: 9, active: false },
        { name: "last_synced_at", friendly_name: "Last Scanned", id: 10, active: false }
      ])

      base.const_set(:DISC_ASSET_UNSELECTED_COLUMNS, [
        { name: "display_name", friendly_name: "Asset Name", type: "string", id: 1, active: false },
        { name: "asset_type", friendly_name: "Type", id: 2, active: false },
        { name: "model", friendly_name: "Model", id: 3, active: false },
        { name: "manufacturer", friendly_name: "Manufacturer", id: 4, active: false },
        { name: "mac_address", friendly_name: "MAC Address", id: 5, active: false },
        { name: "integrations_locations_id", friendly_name: "Location", id: 6, active: false },
        { name: "machine_serial_no", friendly_name: "Serial No.", id: 7, active: false },
        { name: "source", friendly_name: "Sources", id: 8, active: false },
        { name: "created_at", friendly_name: "Discovered On", id: 9, active: false },
        { name: "last_synced_at", friendly_name: "Last Scanned", id: 10, active: false },
        { name: "ip_address", friendly_name: "IP Address", id: 11, active: false },
        { name: "updated_at", friendly_name: "Updated At", id: 12, active: false },
        { name: "os", friendly_name: "OS", id: 13, active: false },
        { name: "os_name", friendly_name: "OS Name", id: 14, active: false },
        { name: "os_version", friendly_name: "OS Version", id: 15, active: false },
        { name: "os_serial_no", friendly_name: "OS Serial No", id: 16, active: false },
        { name: "status", friendly_name: "Status", id: 17, active: false },
        { name: "mac_addresses", friendly_name: "MAC Addresses", id: 18, active: false },
        { name: "secondary_mac_addresses", friendly_name: "Secondary MAC Addresses", id: 19, active: false },
        { name: "system_uuid", friendly_name: "System UUID", id: 20, active: false },
        { name: "discovered_asset_type", friendly_name: "Discovered Asset Category", id: 21, active: false },
        { name: "discovery_details", friendly_name: "Discovery Details", id: 22, active: false },
        { name: "protocols", friendly_name: "Protocols", id: 23, active: false },
        { name: "firmware", friendly_name: "Firmware", id: 24, active: false },
        { name: "system_up_time", friendly_name: "System Uptime", id: 25, active: false },
        { name: "used_by", friendly_name: "Used By", id: 26, active: false },
        { name: "managed_by", friendly_name: "Managed By", id: 27, active: false },
        { name: "asset_tag", friendly_name: "Asset Tag", id: 28, active: false }
      ])
    end
  end
end
